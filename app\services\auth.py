from fastapi import HTT<PERSON>Ex<PERSON>, Header
from app.core.config import settings
from supabase import create_client
from pydantic import BaseModel
from typing import Optional
supabase = create_client(settings.SUPABASE_URL, settings.SUPABASE_KEY)

# Response model
class ProfileResponse(BaseModel):
    id: str
    email: str
    full_name: Optional[str]
    is_verified: bool


def get_current_user(authorization: str = Header(...)):
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid token format")

    token = authorization.split(" ")[1]

    try:
        user_response = supabase.auth.get_user(token)
        if not user_response or not user_response.user:
            raise HTTPException(status_code=404, detail="User not found")

        user = user_response.dict().get("user")

        return ProfileResponse(
            id=user.get("id"),
            email=user.get("email"),
            full_name=user.get("user_metadata", {}).get("full_name"),
            is_verified=user.get("email_confirmed", False)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))