"""Initial migration with nullable fields

Revision ID: fa2d48ae2dd1
Revises: 
Create Date: 2025-08-06 09:29:19.550081

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'fa2d48ae2dd1'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('idx_user_sessions_expires'), table_name='user_sessions')
    op.drop_index(op.f('idx_user_sessions_token'), table_name='user_sessions')
    op.drop_index(op.f('idx_user_sessions_user_id'), table_name='user_sessions')
    op.drop_table('user_sessions')
    op.drop_index(op.f('idx_user_tokens_token'), table_name='user_tokens')
    op.drop_index(op.f('idx_user_tokens_type_expires'), table_name='user_tokens')
    op.drop_index(op.f('idx_user_tokens_user_id'), table_name='user_tokens')
    op.drop_table('user_tokens')
    op.add_column('users', sa.Column('username', sa.String(), nullable=True))
    op.add_column('users', sa.Column('fullname', sa.String(), nullable=True))
    op.alter_column('users', 'email',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.drop_constraint(op.f('users_email_key'), 'users', type_='unique')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.drop_column('users', 'full_name')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('full_name', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_unique_constraint(op.f('users_email_key'), 'users', ['email'], postgresql_nulls_not_distinct=False)
    op.alter_column('users', 'email',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_column('users', 'fullname')
    op.drop_column('users', 'username')
    op.create_table('user_tokens',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('token', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('token_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('expires_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('used_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.CheckConstraint("token_type::text = ANY (ARRAY['email_verification'::character varying, 'password_reset'::character varying]::text[])", name=op.f('user_tokens_token_type_check')),
    sa.PrimaryKeyConstraint('id', name=op.f('user_tokens_pkey')),
    sa.UniqueConstraint('token', name=op.f('user_tokens_token_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('idx_user_tokens_user_id'), 'user_tokens', ['user_id'], unique=False)
    op.create_index(op.f('idx_user_tokens_type_expires'), 'user_tokens', ['token_type', 'expires_at'], unique=False)
    op.create_index(op.f('idx_user_tokens_token'), 'user_tokens', ['token'], unique=False)
    op.create_table('user_sessions',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('session_token', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('expires_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('ip_address', postgresql.INET(), autoincrement=False, nullable=True),
    sa.Column('user_agent', sa.TEXT(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('user_sessions_pkey')),
    sa.UniqueConstraint('session_token', name=op.f('user_sessions_session_token_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('idx_user_sessions_user_id'), 'user_sessions', ['user_id'], unique=False)
    op.create_index(op.f('idx_user_sessions_token'), 'user_sessions', ['session_token'], unique=False)
    op.create_index(op.f('idx_user_sessions_expires'), 'user_sessions', ['expires_at'], unique=False)
    # ### end Alembic commands ###
