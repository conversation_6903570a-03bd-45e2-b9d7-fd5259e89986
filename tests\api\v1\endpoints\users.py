import pytest
from httpx import AsyncClient
from httpx import ASGITransport
from app import app  # make sure this is the FastAPI() instance

@pytest.mark.asyncio
async def test_get_profile(mocker):
    mock_profile = {
        "id": "user123",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "is_verified": True
    }

    mocker.patch("app.services.auth.get_current_user", return_value=mock_profile)
    headers = { 
        "authorization": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IkJYNlE0NzArcVR4VW5rWG0iLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pz4TwOqNwXzL2Dl5q1qjpqr9aI3E7YXWDu_vbeUpvJY"
    }
    
    transport = ASGITransport(app=app)  # 👈 REQUIRED for AsyncClient to work
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        response = await ac.get("/api/v1/users/profile", headers=headers)
    print(response.json())
    assert response.status_code == 200
    data = response.json()

    assert data["email"] == "<EMAIL>"
