from fastapi import APIRouter, Depends, HTTPException, <PERSON><PERSON>
from sqlalchemy.orm import Session
from app.core.db import SessionLocal
from pydantic import BaseModel
from typing import Optional
from  app.api.v1.endpoints import users
from app.api.v1.endpoints import jobs
from supabase import create_client
from app.core.config import settings
from app.services.auth import get_current_user, ProfileResponse

router = APIRouter()

router.include_router(users.router, prefix="/users", tags=["users"])
router.include_router(jobs.router, prefix="/jobs", tags=["jobs"])
