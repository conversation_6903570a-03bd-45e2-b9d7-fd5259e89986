async def extract_custom_structured_data(page, job_id: str) -> dict:
    """Enhanced structured data analysis beyond basic validation"""
    
    structured_data = await page.evaluate('''() => {
        const data = [];
        
        // JSON-LD extraction with validation
        document.querySelectorAll('script[type="application/ld+json"]').forEach(script => {
            try {
                const jsonData = JSON.parse(script.textContent);
                data.push({
                    type: 'json-ld',
                    schema_type: jsonData['@type'] || 'Unknown',
                    data: jsonData,
                    is_valid: true  // We'll validate this in Python
                });
            } catch (e) {
                data.push({
                    type: 'json-ld',
                    error: 'Invalid JSON',
                    raw_content: script.textContent,
                    is_valid: false
                });
            }
        });
        
        return data;
    }''')
    
    # Validate structured data against Schema.org
    validated_data = []
    for item in structured_data:
        if item.get('is_valid'):
            # Validate against Schema.org using jsonschema or custom validation
            validation_result = validate_schema_org(item['data'])
            item['validation'] = validation_result
        
        validated_data.append(item)
    
    return {
        'structured_data': validated_data,
        'schema_count': len([d for d in validated_data if d.get('is_valid')]),
        'validation_errors': [d for d in validated_data if not d.get('is_valid')]
    }

# Helper functions
def validate_schema_org(data: dict) -> dict:
    """Validate structured data against Schema.org standards"""
    # Implementation depends on your validation library
    # Could use jsonschema with Schema.org schemas
    pass
