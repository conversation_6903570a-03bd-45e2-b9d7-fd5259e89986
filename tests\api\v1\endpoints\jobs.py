from fastapi.testclient import TestClient
from app import app

client = TestClient(app)

def test_create_search_job():
    # Simulate login token and job creation (this will fail without mock or real token)
    headers = { 
        "authorization": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IkJYNlE0NzArcVR4VW5rWG0iLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pz4TwOqNwXzL2Dl5q1qjpqr9aI3E7YXWDu_vbeUpvJY"}
    response = client.post("api/v1/job/search", json={
	"job_id": "e8ce802b-d29d-4b8a-96f5-67b9f985ccfb",
	"status": "pending",
	"search_param": "hello"
}, headers=headers)
    
    # You expect this to fail without auth, but test the response status 
    assert response.status_code in (200, 401)
