"""Browser pool management for Play<PERSON>"""
import asyncio
from contextlib import asynccontextmanager
import logging
import sys
import subprocess
import platform
import os
from playwright.async_api import async_playwright

logger = logging.getLogger(__name__)

class BrowserPool:
    def __init__(self):
        self.playwright = None
        self.browser = None
        self._lock = asyncio.Lock()
        self._browsers_installed = False

    async def _install_browsers(self):
        """Install Playwright browsers if not already installed"""
        if self._browsers_installed:
            return

        try:
            logger.info("Installing Playwright browsers...")

            # Set environment variables for Windows compatibility
            env = os.environ.copy()
            if platform.system() == "Windows":
                env["PLAYWRIGHT_BROWSERS_PATH"] = "0"  # Use default path

            result = subprocess.run(
                [sys.executable, "-m", "playwright", "install", "chromium"],
                capture_output=True,
                text=True,
                timeout=300,
                env=env
            )
            if result.returncode == 0:
                self._browsers_installed = True
                logger.info("Playwright browsers installed successfully")
            else:
                logger.error(f"Failed to install browsers: {result.stderr}")
                # Don't fail completely, try to continue
        except Exception as e:
            logger.error(f"Error installing browsers: {str(e)}")
            # Don't fail completely, try to continue

    async def start(self):
        """Initialize the browser pool"""
        if self.playwright is None:
            # Try to install browsers first
            await self._install_browsers()

            self.playwright = await async_playwright().start()

            # Windows-specific browser launch options
            launch_options = {
                'headless': True,
                'args': [
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            }

            # Additional Windows-specific options
            if platform.system() == "Windows":
                launch_options['args'].extend([
                    '--disable-gpu',
                    '--no-first-run',
                    '--disable-default-apps'
                ])

            self.browser = await self.playwright.chromium.launch(**launch_options)
            logger.info("Browser pool started successfully")
    
    async def stop(self):
        """Stop the browser pool"""
        if self.browser:
            await self.browser.close()
            self.browser = None
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None
            logger.info("Browser pool stopped")
    
    @asynccontextmanager
    async def get_context(self):
        """Get a browser context"""
        await self.start()
        context = await self.browser.new_context()
        try:
            yield context
        finally:
            await context.close()

# Global browser pool instance
browser_pool = BrowserPool()
