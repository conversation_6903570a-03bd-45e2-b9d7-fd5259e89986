from fastapi import APIRouter, Depends, HTTPException, Header
from supabase import create_client
from pydantic import BaseModel, EmailStr
from sqlalchemy.orm import Session
from app.core.db import SessionLocal, get_db 
from typing import Optional
from app.core.config import settings 
from app.services.auth import get_current_user, ProfileResponse 
from app.core.config import settings

router = APIRouter()
supabase = create_client(settings.SUPABASE_URL, settings.SUPABASE_KEY)





@router.get("/profile", response_model=ProfileResponse, tags=["users"])
async def get_profile(
    authorization: str = Header(...),
    db: Session = Depends(get_db),
    current_user: ProfileResponse = Depends(get_current_user)
):
    return get_current_user(authorization)