from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.base import BaseHTTPMiddleware

from app.api import router as api_router
from app.core.logger import setup_logging
from app.services.exceptions import global_exception_handler, http_exception_handler
from app.core.rate_limiter import GlobalRateLimitMiddleware, limiter

from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

import logging

# Setup logging
setup_logging()

# FastAPI app
app = FastAPI(
    title="FastAPI App",
    description="Truedax FastAPI Application",
    version="1.0.0",
    contact={
        "name": "<PERSON><PERSON><PERSON>",
        "email": "<EMAIL>"
    }
)

# CORS setup
origins = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global exception handlers
app.add_exception_handler(Exception, global_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
app.state.limiter = limiter
app.add_middleware(GlobalRateLimitMiddleware)

# API router
app.include_router(api_router, prefix="/api/v1") 

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logging.info("Application startup complete")
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup services on shutdown"""
    logging.info("Application shutdown complete")

# Health check endpoint
@app.get("/")
def health_check():
    logging.info("Health check endpoint was called.")
    return {"status": "ok"}
