# Lighthouse CLI with comprehensive audits
import subprocess
import json
import tempfile
import os
import logging

logger = logging.getLogger(__name__)

async def run_lighthouse_comprehensive(page, url: str, job_id: str) -> dict:
    """Run comprehensive Lighthouse analysis"""

    try:
        # Check if lighthouse is available
        check_result = subprocess.run(['lighthouse', '--version'],
                                    capture_output=True, text=True, timeout=10)
        if check_result.returncode != 0:
            logger.warning("Lighthouse CLI not available, skipping lighthouse analysis")
            return {'error': 'Lighthouse CLI not available', 'basic_metrics': await get_basic_metrics(page)}

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            result = subprocess.run([
                'lighthouse', url,
                '--output=json',
                '--output-path=' + f.name,
                '--chrome-flags="--headless --no-sandbox"',
                '--only-categories=seo,accessibility,performance,best-practices',
                '--quiet'
            ], capture_output=True, text=True, timeout=90)

            if result.returncode == 0:
                with open(f.name, 'r') as json_file:
                    lighthouse_data = json.load(json_file)

                # Extract and structure the data we need
                return process_lighthouse_data(lighthouse_data)
            else:
                logger.error(f"Lighthouse failed with return code {result.returncode}: {result.stderr}")
                return {'error': f'Lighthouse execution failed: {result.stderr}', 'basic_metrics': await get_basic_metrics(page)}

    except Exception as e:
        logger.error(f"Lighthouse analysis failed: {str(e)}")
        return {'error': f'Lighthouse analysis failed: {str(e)}', 'basic_metrics': await get_basic_metrics(page)}

"""Process Lighthouse data into our structured format"""
def process_lighthouse_data(lighthouse_data: dict) -> dict:
    
    categories = lighthouse_data.get('categories', {})
    audits = lighthouse_data.get('audits', {})
    
    # Core scores
    scores = {
        'seo_score': categories.get('seo', {}).get('score', 0) * 100,
        'performance_score': categories.get('performance', {}).get('score', 0) * 100,
        'accessibility_score': categories.get('accessibility', {}).get('score', 0) * 100,
        'best_practices_score': categories.get('best-practices', {}).get('score', 0) * 100
    }
    
    # Detailed SEO breakdown from Lighthouse audits
    seo_details = {
        'meta_description': {
            'status': audits.get('meta-description', {}).get('score'),
            'details': audits.get('meta-description', {}).get('explanation', '')
        },
        'document_title': {
            'status': audits.get('document-title', {}).get('score'),
            'details': audits.get('document-title', {}).get('explanation', '')
        },
        'heading_order': {
            'status': audits.get('heading-order', {}).get('score'),
            'details': audits.get('heading-order', {}).get('explanation', '')
        },
        'image_alt': {
            'status': audits.get('image-alt', {}).get('score'),
            'missing_count': len(audits.get('image-alt', {}).get('details', {}).get('items', []))
        },
        'link_text': {
            'status': audits.get('link-text', {}).get('score'),
            'issues': audits.get('link-text', {}).get('details', {}).get('items', [])
        },
        'canonical': {
            'status': audits.get('canonical', {}).get('score'),
            'details': audits.get('canonical', {}).get('explanation', '')
        },
        'robots_txt': {
            'status': audits.get('robots-txt', {}).get('score'),
            'details': audits.get('robots-txt', {}).get('explanation', '')
        },
        'hreflang': {
            'status': audits.get('hreflang', {}).get('score'),
            'details': audits.get('hreflang', {}).get('explanation', '')
        }
    }
    
    # Performance metrics (important for SEO)
    performance_details = {
        'first_contentful_paint': audits.get('first-contentful-paint', {}).get('numericValue'),
        'largest_contentful_paint': audits.get('largest-contentful-paint', {}).get('numericValue'),
        'cumulative_layout_shift': audits.get('cumulative-layout-shift', {}).get('numericValue'),
        'total_blocking_time': audits.get('total-blocking-time', {}).get('numericValue'),
        'speed_index': audits.get('speed-index', {}).get('numericValue')
    }
    
    return {
        'scores': scores,
        'seo_audit': seo_details,
        'performance_metrics': performance_details,
        'opportunities': extract_opportunities(audits),
        'diagnostics': extract_diagnostics(audits)
    }

async def get_basic_metrics(page) -> dict:
    """Get basic metrics when Lighthouse is not available"""
    try:
        # Get basic page metrics using Playwright
        metrics = await page.evaluate('''() => {
            return {
                title: document.title,
                meta_description: document.querySelector('meta[name="description"]')?.content || '',
                h1_count: document.querySelectorAll('h1').length,
                images_without_alt: Array.from(document.querySelectorAll('img')).filter(img => !img.alt).length,
                total_images: document.querySelectorAll('img').length,
                links_count: document.querySelectorAll('a').length,
                has_canonical: !!document.querySelector('link[rel="canonical"]'),
                viewport_meta: !!document.querySelector('meta[name="viewport"]'),
                lang_attribute: document.documentElement.lang || 'not-set'
            };
        }''')

        return {
            'basic_seo': {
                'title_present': bool(metrics.get('title')),
                'meta_description_present': bool(metrics.get('meta_description')),
                'h1_count': metrics.get('h1_count', 0),
                'images_without_alt': metrics.get('images_without_alt', 0),
                'total_images': metrics.get('total_images', 0),
                'has_canonical': metrics.get('has_canonical', False),
                'has_viewport_meta': metrics.get('viewport_meta', False),
                'lang_attribute': metrics.get('lang_attribute', 'not-set')
            }
        }
    except Exception as e:
        return {'error': f'Failed to get basic metrics: {str(e)}'}

def extract_opportunities(audits: dict) -> list:
    """Extract performance opportunities from Lighthouse audits"""
    opportunities = []

    # Common performance opportunities
    opportunity_audits = [
        'unused-css-rules', 'unused-javascript', 'modern-image-formats',
        'offscreen-images', 'render-blocking-resources', 'unminified-css',
        'unminified-javascript', 'efficient-animated-content'
    ]

    for audit_key in opportunity_audits:
        audit = audits.get(audit_key, {})
        if audit.get('score') is not None and audit.get('score') < 1:
            opportunities.append({
                'audit': audit_key,
                'title': audit.get('title', ''),
                'description': audit.get('description', ''),
                'potential_savings': audit.get('details', {}).get('overallSavingsMs', 0)
            })

    return opportunities

def extract_diagnostics(audits: dict) -> list:
    """Extract diagnostic information from Lighthouse audits"""
    diagnostics = []

    # Common diagnostic audits
    diagnostic_audits = [
        'dom-size', 'critical-request-chains', 'main-thread-tasks',
        'bootup-time', 'uses-long-cache-ttl', 'total-byte-weight'
    ]

    for audit_key in diagnostic_audits:
        audit = audits.get(audit_key, {})
        if audit.get('score') is not None:
            diagnostics.append({
                'audit': audit_key,
                'title': audit.get('title', ''),
                'description': audit.get('description', ''),
                'score': audit.get('score'),
                'numeric_value': audit.get('numericValue')
            })

    return diagnostics
