# Core FastAPI dependencies
fastapi==0.116.1
uvicorn==0.35.0
pydantic==2.11.7
pydantic-settings==2.10.1
starlette==0.47.2

# Database
SQLAlchemy==2.0.42
psycopg2-binary==2.9.10
alembic==1.16.4

# Authentication and security
PyJWT==2.10.1
supabase==2.17.0
gotrue==2.12.3

# Rate limiting
slowapi==0.1.9
limits==5.4.0

# Environment and configuration
python-dotenv==1.1.1
pydantic-core==2.33.2

# HTTP clients
httpx==0.28.1
httpcore==1.0.9

# Testing
pytest==8.4.1
pytest-asyncio==1.1.0

# Content extraction and NLP
readability-lxml
spacy
beautifulsoup4

# Data validation
jsonschema

# Progress tracking
tqdm

# Additional dependencies
annotated-types==0.7.0
anyio==4.9.0
certifi==2025.8.3
click==8.2.1
colorama==0.4.6
Deprecated==1.2.18
deprecation==2.1.0
dnspython==2.7.0
email_validator==2.2.0
greenlet==3.2.3
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httptools==0.6.4
hyperframe==6.1.0
idna==3.10
iniconfig==2.1.0
Mako==1.3.10
MarkupSafe==3.0.2
packaging==25.0
pluggy==1.6.0
postgrest==1.1.1
Pygments==2.19.2
python-dateutil==2.9.0.post0
PyYAML==6.0.2
realtime==2.6.0
six==1.17.0
sniffio==1.3.1
storage3==0.12.0
StrEnum==0.4.15
supafunc==0.10.1
typing-inspection==0.4.1
typing_extensions==4.14.1
watchfiles==1.1.0
websockets==15.0.1
wrapt==1.17.2

# Browser automation (for readability analysis)
playwright
