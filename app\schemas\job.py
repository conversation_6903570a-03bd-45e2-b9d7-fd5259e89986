from pydantic import BaseModel
from enum import Enum
from uuid import UUID

class JobStatus(str, Enum):
    pending = "pending"
    running = "running"
    failed = "failed"
    completed = "completed"
    canceled = "canceled"

class JobCreate(BaseModel):
    search_param: str

class JobResponse(BaseModel):
    job_id: UUID
    status: JobStatus
    search_param: str
# Input schema
class SearchRequest(BaseModel):
    search_param: str

# Output schema
class SearchResponse(BaseModel):
    job_id: UUID
    status: JobStatus
    search_param: str