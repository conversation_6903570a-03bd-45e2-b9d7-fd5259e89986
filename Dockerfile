# Use a slim base image
FROM python:3.11-slim AS base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set working directory
WORKDIR /app

# Install virtualenv
RUN pip install --no-cache-dir virtualenv

# Copy project files
COPY . .

# Create a virtual environment
RUN virtualenv venv

# Activate virtualenv and install dependencies
RUN . venv/bin/activate && pip install --no-cache-dir -r requirements.txt

# Expose port
EXPOSE 8000

# Run the application
CMD ["/bin/bash", "-c", ". venv/bin/activate && uvicorn app.main:app --host 0.0.0.0 --port 8000"]
# Use a slim base image