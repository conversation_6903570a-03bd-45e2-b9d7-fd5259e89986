from pydantic_settings import BaseSettings
from pydantic import Field
class Settings(BaseSettings):
    SUPABASE_URL: str
    SUPABASE_KEY: str
    db_host: str = Field(..., env="DB_HOST")
    db_port: int = Field(..., env="DB_PORT")
    db_name: str = Field(..., env="DB_NAME")
    db_user: str = Field(..., env="DB_USER")
    db_password: str = Field(..., env="DB_PASSWORD") 
    RATE_LIMIT_REQUESTS: int = Field(..., env="RATE_LIMIT_REQUESTS") 
    RATE_LIMIT_SECONDS: int = Field(..., env="RATE_LIMIT_SECONDS") 
    DB_URL: str = Field(..., env="DB_URL")     
    SERVER_PORT: int = Field(8000, env="SERVER_PORT")  # Default to 8000 if not set


    class Config:
        env_file = ".env"
        extra = "forbid" 

settings = Settings()