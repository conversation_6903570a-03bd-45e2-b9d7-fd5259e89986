from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.config import settings 
from slowapi.util import get_remote_address
from slowapi import Limiter


import logging
# Create limiter instance using client IP
limiter = Limiter(key_func=get_remote_address)

# Define rate limit from .env (e.g., 5/60s)
RATE_LIMIT = f"{settings.RATE_LIMIT_REQUESTS} per {settings.RATE_LIMIT_SECONDS} second"



# Global rate limiting middleware
class GlobalRateLimitMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await limiter.limit(RATE_LIMIT)(call_next)(request)
        return response