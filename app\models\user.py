# models/user.py
from sqlalchemy import Column, <PERSON>te<PERSON>, String, Boolean, DateTime, func
from app.core.db import Base
from sqlalchemy.dialects.postgresql import UUID
import uuid
class User(Base):
    __tablename__ = "users"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    username = Column(String, unique=True, index=True, nullable=True)
    email = Column(String, unique=True, index=True, nullable=False)
    fullname = Column(String, nullable=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
