from sqlalchemy import Column, String, DateTime, Enum, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import enum
import uuid
from app.models.user import User 

from app.core.db import Base


# Define job status enum
class JobStatus(str, enum.Enum):
    pending = "pending"
    running = "running"
    failed = "failed"
    completed = "completed"
    canceled = "canceled"


class Job(Base):
    __tablename__ = "job"

    job_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    status = Column(Enum(JobStatus, name="job_status"), nullable=False, default=JobStatus.pending)
    user_id = Column(UUID(as_uuid=True), ForeignKey(User.id), nullable=False)
    search_param = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    def __repr__(self):
        return f"<Job(id={self.job_id}, status={self.status}, user_id={self.user_id})>"
