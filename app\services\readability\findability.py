"""Custom metrics for AI readiness that Lighthouse doesn't cover"""
async def extract_ai_readiness_metrics(page, job_id: str) -> dict:
    
    ai_metrics = await page.evaluate('''() => {
        return {
            // Content clarity for AI
            content_sections: document.querySelectorAll('section, article, div[class*="content"]').length,
            
            // Navigation clarity
            breadcrumbs: document.querySelector('[aria-label*="breadcrumb"], .breadcrumb, nav ol') ? true : false,
            
            // Content organization
            has_table_of_contents: document.querySelector('[class*="toc"], [id*="toc"]') ? true : false,
            
            // FAQ sections (great for AI)
            faq_sections: document.querySelectorAll('[class*="faq"], [class*="question"]').length,
            
            // Call-to-action clarity
            cta_buttons: document.querySelectorAll('button, [class*="cta"], [class*="call-to-action"]').length,
            
            // Contact information visibility
            contact_info: {
                email: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/.test(document.body.textContent),
                phone: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/.test(document.body.textContent),
                address: document.querySelector('[class*="address"]') ? true : false
            }
        };
    }''')
    
    return {
        'content_organization_score': calculate_content_organization_score(ai_metrics),
        'ai_findability_score': calculate_ai_findability_score(ai_metrics),
        'details': ai_metrics
    }


"""Calculate how well-organized content is for AI consumption"""
def calculate_content_organization_score(metrics: dict) -> int:
    score = 0
    if metrics.get('breadcrumbs'): score += 20
    if metrics.get('has_table_of_contents'): score += 20
    if metrics.get('content_sections', 0) > 3: score += 20
    if metrics.get('faq_sections', 0) > 0: score += 20
    return min(score, 100)

"""Calculate how easily AI can find key information"""
def calculate_ai_findability_score(metrics: dict) -> int:
    score = 0
    contact = metrics.get('contact_info', {})
    if contact.get('email'): score += 15
    if contact.get('phone'): score += 15
    if contact.get('address'): score += 10
    if metrics.get('cta_buttons', 0) > 0: score += 25
    if metrics.get('faq_sections', 0) > 2: score += 35
    return min(score, 100)