"""Extract content specifically for AI/LLM analysis"""
from readability import Document
import logging
from bs4 import BeautifulSoup
from collections import Counter
import re

logger = logging.getLogger(__name__)

async def extract_content_for_ai_analysis(page, job_id: str) -> dict:
    
    # Get rendered HTML
    html_content = await page.content()
    
    # Extract main content using readability
    doc = Document(html_content)
    clean_content = doc.summary()

    # Extract clean text from the summary
    soup = BeautifulSoup(clean_content, 'html.parser')
    main_text = soup.get_text()
    
    # Extract content structure for AI analysis
    content_structure = await page.evaluate('''() => {
        return {
            // Content hierarchy for AI understanding
            article_content: document.querySelector('article')?.textContent?.trim() || 
                           document.querySelector('main')?.textContent?.trim() || 
                           document.body.textContent.trim(),
            
            // Key content elements
            title: document.title,
            headings: Array.from(document.querySelectorAll('h1,h2,h3,h4,h5,h6')).map(h => ({
                level: parseInt(h.tagName.substring(1)),
                text: h.textContent.trim()
            })),
            
            // Content for AI to understand context
            lists: Array.from(document.querySelectorAll('ul, ol')).map(list => 
                Array.from(list.children).map(li => li.textContent.trim())
            ),
            
            // Navigation structure (helps AI understand site organization)
            nav_items: Array.from(document.querySelectorAll('nav a')).map(a => ({
                text: a.textContent.trim(),
                href: a.href
            }))
        };
    }''')
    
    # Simple entity extraction (without spaCy)
    entities = extract_simple_entities(main_text)

    return {
        'main_content': clean_content,
        'full_text': main_text,
        'content_structure': content_structure,
        'entities': entities,
        'keywords': extract_keywords(main_text),
        'readability_score': calculate_readability(main_text),
        'word_count': len(main_text.split()),
        'content_topics': extract_topics(main_text)
    }

def extract_keywords(text: str) -> list:
    """Extract basic keywords from text"""
    try:
        # Simple keyword extraction - split by common delimiters and filter
        words = text.lower().split()
        # Remove common stop words and short words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        keywords = [word.strip('.,!?;:"()[]{}') for word in words if len(word) > 3 and word not in stop_words]
        # Return top 20 most frequent keywords
        return [word for word, count in Counter(keywords).most_common(20)]
    except Exception:
        return []

def calculate_readability(text: str) -> float:
    """Calculate basic readability score"""
    try:
        if not text:
            return 0

        # Simple readability calculation based on sentence and word length
        sentences = text.split('.')
        words = text.split()

        if len(sentences) == 0 or len(words) == 0:
            return 0

        avg_sentence_length = len(words) / len(sentences)
        avg_word_length = sum(len(word) for word in words) / len(words)

        # Simple readability score (lower is better)
        score = max(0, 100 - (avg_sentence_length * 2) - (avg_word_length * 5))
        return round(score, 2)
    except Exception:
        return 0

def extract_topics(text: str) -> list:
    """Extract basic topics from text"""
    try:
        # Simple topic extraction based on common patterns
        topics = []
        text_lower = text.lower()

        # Common topic indicators
        topic_keywords = {
            'technology': ['software', 'computer', 'digital', 'tech', 'app', 'website', 'online'],
            'business': ['company', 'business', 'service', 'product', 'customer', 'market'],
            'education': ['learn', 'education', 'course', 'training', 'skill', 'knowledge'],
            'health': ['health', 'medical', 'doctor', 'treatment', 'wellness', 'care'],
            'finance': ['money', 'financial', 'investment', 'bank', 'payment', 'cost']
        }

        for topic, keywords in topic_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                topics.append(topic)

        return topics[:5]  # Return top 5 topics
    except Exception:
        return []

def extract_simple_entities(text: str) -> list:
    """Extract simple entities without spaCy"""
    try:
        entities = []

        # Simple email detection
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        for email in emails:
            entities.append({"text": email, "label": "EMAIL"})

        # Simple URL detection
        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)
        for url in urls:
            entities.append({"text": url, "label": "URL"})

        # Simple phone number detection
        phones = re.findall(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', text)
        for phone in phones:
            entities.append({"text": phone, "label": "PHONE"})

        # Simple capitalized words (potential names/organizations)
        capitalized = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', text)
        for cap in capitalized[:10]:  # Limit to first 10
            if len(cap) > 2:
                entities.append({"text": cap, "label": "PERSON_OR_ORG"})

        return entities[:20]  # Return top 20 entities
    except Exception:
        return []
